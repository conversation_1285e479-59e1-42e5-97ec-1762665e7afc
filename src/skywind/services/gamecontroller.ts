import {
    BalanceResponse,
    BalanceTransferRequest,
    BaseRequest,
    BaseResult,
    ClientResponse,
    ClientSettings,
    ExtraData,
    GameInitRequest,
    GamePlayRequest,
    GamePlayResponse,
    GameStateInfoRequest,
    JackpotGame,
    JackpotResults,
    JackpotTickerRequest,
    LiveGamePlayResponse,
    PushService,
    SomeGame,
    TickersResponse
} from "@skywind-group/sw-game-core";
import { load, LoadedGameResult } from "./game/game";
import { GameData, getService as getAuthService, StartGameResult } from "./auth";
import { StartGameTokenData, verifyReplayToken, verifySiteRefToken } from "./tokens";
import { logging, measures } from "@skywind-group/sw-utils";
import { EngineGameFlow } from "./gameflow";

import {
    ExtendedGameInitRequest,
    GameFlowContext,
    MerchantLogoutResult, ReplayRequest,
    RequestContext,
    RequestReferrer,
    RoundStatistics
} from "./context/gamecontext";
import * as Errors from "../errors";
import {
    GameError, GameHistoryDetailsNotFound,
    GameNotMatch,
    isSWError,
    JackpotStatusNotValid,
    ManualRedeemBNSError,
    RedeemBNSError
} from "../errors";
import { GameFlowFactory } from "./gameFlowFactory";
import GameInitSettings from "./gameInitSettings";
import { injectRandomGenerator, isAllowedSetPositionsByClient } from "./random";
import { ChooseGameRequest, GamesListRequest, getService as getPlayerGameInfoService } from "./playerGameInfo";
import config from "../config";
import { getRegulatoryActionService } from "./regulatoryActionService";
import {
    ChangeNicknameActionRequest,
    extractStartGameTokenData,
    GameReInitRequest,
    PlayerActionRequest,
    RedeemBNSRequest,
    RedeemBNSResponse,
    ReInitClientResponse,
} from "./requests";
import MerchantGameSessionService from "./merchantGameSessionService";
import { initJackpot } from "./jpn/jackpot";
import { getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { GameContextID } from "./contextIds";
import PendingCompletionService from "./pendingCompletionService";
import { getMerchantKeepAliveService } from "./merchantKeepAlive";
import { ContextVariables } from "../utils/contextVariables";
import { getChangeNicknameActionService } from "./changeNicknameActionService";
import { JackpotResult } from "@skywind-group/sw-game-core/src/definition";
import { validateCountry } from "../utils/countries";
import { JackpotUtil } from "./jpn/jackpotUtil";
import { ERROR_LEVEL, ErrorSpecialFlag, SWError } from "@skywind-group/sw-wallet-adapter-core";
import measure = measures.measure;
import GameHistoryService from "./playerGameHistory";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { PlayMode } from "./playMode";

const flowDataLogger = logging.logger("sw-slot-engine:gamecontroller:flow");

export type PlayMethod = (EngineGameFlow) => Promise<GamePlayResponse>;

/**
 *  Implementation of game controller
 */
export abstract class AbstractGameController {

    constructor(protected pushService?: PushService) {
    }

    public process<T extends BaseRequest>(request: T): Promise<ClientResponse> {
        ContextVariables.setUpForRequest(request);
        const req = request as any;
        if (request.request === "init") {
            return this.init(req);
        } else if (request.request === "reinit") {
            return this.reInit(req);
        } else if (request.request === "transfer-in" || request.request === "transfer-out") {
            return this.transfer(req);
        } else if (request.request === "jp-ticker") {
            return this.getJackpotTicker(req);
        } else if (request.request === "balance") {
            return this.getPlayerBalance(req);
        } else if (request.request === "choose-game") {
            return this.chooseGame(req);
        } else if (request.request === "state") {
            return this.getState(req);
        } else if (request.request === "redeem-bns") {
            return this.redeemBNS(req);
        } else if (request.request === "get-games") {
            return this.getAvailableGames(req);
        } else if (request.request === "player-action") {
            return this.performPlayerAction(req);
        } else if (request.request === "keep-alive") {
            return this.keepAlive(req);
        } else if (request.request === "change-nickname") {
            return this.changeNickname(req);
        } else if (request.request === "replay") {
            return this.replay(req);
        } else {
            return this.play(req);
        }
    }

    /**
     * Process 'init' game init
     * @param request game init request
     */
    @measure({ name: "GameController.init", isAsync: true })
    public async init(request: ExtendedGameInitRequest): Promise<ClientResponse> {
        const startGameTokenData: StartGameTokenData = await this.validateInitRequest(request);
        ContextVariables.setUpForInit(startGameTokenData);
        let siteAuthCookie;
        if (request.referrer) {
            const referrer = await verifySiteRefToken(request.referrer);
            siteAuthCookie = "siteRefCookie=" + referrer.siteReferrer;
        }

        const ctx = await getGameFlowContextManager(startGameTokenData.playmode)
            .findOrRestoreGameContext(GameContextID.create(startGameTokenData.gameCode,
                startGameTokenData.brandId,
                startGameTokenData.playerCode,
                request.deviceId, startGameTokenData.playmode));

        this.checkIfMerchantSessionStateful(ctx, startGameTokenData);

        if (ctx?.requireLogout) {
            await PendingCompletionService.completeBeforeRelaunch(ctx);
        }

        const { gameId, startGameToken, ip, language, deviceId, deviceData } = request;

        const loadResult: LoadedGameResult = await this.loadGame(request.gameId);

        const startGameResult: StartGameResult = await getAuthService(startGameTokenData.playmode)
            .startGame(gameId,
                startGameToken,
                ip,
                siteAuthCookie,
                language,
                deviceId,
                deviceData,
                loadResult?.moduleName?.version);
        if (startGameTokenData.referrer) {
            startGameResult.gameData.referrer = startGameTokenData.referrer;
        }
        if (validateCountry(startGameTokenData.operatorCountry)) {
            startGameResult.gameData.operatorCountry = startGameTokenData.operatorCountry;
        }
        // saving ip from init request
        if (ip) {
            startGameResult.gameData.ip = ip;
        }
        if (startGameTokenData.lobbySessionId) {
            startGameResult.gameData.lobbySessionId = startGameTokenData.lobbySessionId;
        }

        const [jpContext, jackpots] = await initJackpot(startGameResult.gameData);

        try {
            const flow: EngineGameFlow<GameInitRequest> = await GameFlowFactory.createForInit(
                ctx,
                startGameResult.gameData,
                request,
                loadResult,
                this.pushService,
                jpContext,
                jackpots,
                startGameTokenData.isNewSession);
            flowDataLogger.info(flow.getLogData(), "Init");

            return this.doInit(flow, loadResult, startGameResult);
        } catch (e) {
            // set context to controller to be able to log out session even if pending fails
            this.setUpContext(ctx, loadResult.game);
            throw e;
        }
    }

    @measure({ name: "GameController.reInit", isAsync: true })
    public async reInit(request: GameReInitRequest & RequestContext & RequestReferrer): Promise<ClientResponse> {
        const loadedGameResult = await this.loadGame(request.gameId);
        const flow = await GameFlowFactory.createForReInit(request, loadedGameResult, this.pushService);

        const result: ReInitClientResponse = await this.doInit(flow, loadedGameResult, undefined, true);
        result.lastRequestId = flow.lastRequestId;
        return result;
    }

    @measure({ name: "GameController.replay", isAsync: true })
    public async replay(request: ReplayRequest): Promise<ClientResponse> {
        await verifyReplayToken(request.replayToken);
        const replayResponses = await GameHistoryService.getReplayEvents(request.replayToken);
        if (!replayResponses?.length) {
            throw new GameHistoryDetailsNotFound();
        }
        const [firstReplayResponse] = replayResponses;
        const { gameVersion, gameId: gameCode, details, currency } = replayResponses[0];
        const gameVersionResponse = await GameHistoryService.getReplayGameVersion({
            replayToken: request.replayToken,
            gameVersion,
            gameCode
        });
        const stake = details.stake?.bet;
        if (stake) {
            gameVersionResponse?.initSettings?.settings?.stakeAll.push(stake);
        }
        let roundTotalBet = 0;
        let roundTotalWin = 0;
        const currentCurrency: Currency = Currencies.get(currency);
        return {
            result: gameVersionResponse?.initSettings,
            jrsdSettings: gameVersionResponse?.jrsdSettings,
            jurisdictionCode: gameVersionResponse?.jurisdictionCode,
            settings: gameVersionResponse?.settings,
            gameSettings: gameVersionResponse?.gameSettings,
            brandSettings: gameVersionResponse?.brandSettings,
            replayResponses: replayResponses.map(replayResponse => {
                const response = {
                    balance: this.getReplayBalance(replayResponse.balanceAfter, replayResponse.currency),
                    result: replayResponse.details,
                    roundEnded: !!replayResponse.endOfRound,
                } as any;

                roundTotalBet += (replayResponse.details?.totalBet || 0);
                roundTotalWin += (replayResponse.details?.totalWin || 0);
                response.roundTotalBet = currentCurrency.format(roundTotalBet);
                response.roundTotalWin = currentCurrency.format(roundTotalWin);
                return response;
            }),
            balance: this.getReplayBalance(firstReplayResponse.balanceBefore, firstReplayResponse.currency)
        } as any;
    }

    private getReplayBalance(amount: number, currency: string): BalanceResponse {
        return {
            currency: currency,
            amount,
            real: {
                amount
            },
            bonus: {
                amount: 0
            }
        };
    }

    @measure({ name: "GameController.play", isAsync: true })
    public async play(request: GamePlayRequest): Promise<ClientResponse> {
        const flow: EngineGameFlow<GamePlayRequest> = await this.createFlowForRequest(request, true, true);
        flowDataLogger.info(flow.getLogData(), "Play");
        return this.processPlay(flow, request);
    }

    @measure({ name: "GameController.performPlayerAction", isAsync: true })
    public async performPlayerAction(request: PlayerActionRequest): Promise<ClientResponse> {
        flowDataLogger.info({ request }, "Player-action");
        const service = getRegulatoryActionService();
        const flow: EngineGameFlow<GamePlayRequest> = await this.createFlowForRequest(request, true, false);
        const actionResponse = await service.performPlayerRegulatoryAction(request, flow.flowContext);

        const response: ClientResponse = {
            gameSession: request.gameSession,
            balance: undefined,
            result: { request: request.request }
        };

        if (actionResponse) {
            response["actionResponse"] = actionResponse;
        }

        this.checkAndStoreRegulatoryData(actionResponse, flow.flowContext);

        await flow.flowContext.updateGameData();

        return response;
    }

    private checkAndStoreRegulatoryData(actionResponse: any, context: GameFlowContext): void {
        const regulatoryData = actionResponse?.extraData?.regulatoryData;
        if (regulatoryData) {
            context.gameData.gameTokenData.regulatoryData = regulatoryData;
        }
    }

    @measure({ name: "GameController.keepAlive", isAsync: true })
    public async keepAlive(req: BaseRequest): Promise<ClientResponse> {
        const flow = await this.createFlowForRequest(req, false, false);

        await getMerchantKeepAliveService().ping(flow);

        return {
            gameSession: req.gameSession,
            balance: undefined,
            result: { request: req.request }
        };
    }

    public async internalEvent(request: GamePlayRequest): Promise<ClientResponse> {
        const flow: EngineGameFlow<GamePlayRequest> = await this.createFlowForRequest(request, false, true);
        flowDataLogger.info(flow.getLogData(), "Internal event");
        if (!flow.game.internalEvent) {
            return Promise.reject(new Errors.InternalEventIsNotSupported());
        }
        return this.processPlay(flow, request, this.gameInternalEvent.bind(this));
    }

    @measure({ name: "GameController.transfer", isAsync: true })
    public async transfer(req: BalanceTransferRequest): Promise<ClientResponse> {
        if (!req.amount) {
            return Promise.reject(new Errors.InvalidBalanceTransfer());
        }
        const flow: EngineGameFlow<BalanceTransferRequest> = await this.createFlowForRequest(req, false, true);
        return this.transferBalance(flow, req);
    }

    @measure({ name: "GameController.getJackpotTicker", isAsync: true })
    public async getJackpotTicker(req: JackpotTickerRequest): Promise<ClientResponse> {
        const flow: EngineGameFlow<JackpotTickerRequest> = await this.createFlowForRequest(req, false, false);
        return this.getTicker(flow, req);
    }

    public async getPlayerBalance(req: BaseRequest): Promise<ClientResponse> {
        const flow = await this.createFlowForRequest(req, false, false);
        const balance: BalanceResponse = await this.getBalance(flow);

        const extraData: ExtraData = flow.getExtraData();
        return {
            gameSession: flow.info().gameSessionId,
            balance: balance,
            extraData,
            result: { request: req.request }
        };
    }

    @measure({ name: "GameController.stateInfo", isAsync: true })
    public async getState(req: GameStateInfoRequest): Promise<ClientResponse> {
        const flow: EngineGameFlow<GameStateInfoRequest> = await this.createFlowForRequest(req, false, true);
        injectRandomGenerator(flow);
        const game = flow.game;
        const balance = req.getBalance && await this.getBalance(flow);
        const tickers = req.getTickers && await this.gameJackpotTickers(flow);
        const response = this.createResponse(req, flow, balance, { request: req.request }, tickers);

        if (game.stateInfo) {
            response.result = await game.stateInfo(flow).catch(this.wrapGameError);
        }

        return response;
    }

    protected abstract createFlowForRequest<T extends BaseRequest = BaseRequest>(req: T,
                                                                                 checkConcurrency?: boolean,
                                                                                 checkPending?: boolean)
    // tslint:disable-next-line:typedef-whitespace
        : Promise<EngineGameFlow<T>>;

    protected async doInit(flow: EngineGameFlow<GameInitRequest>,
                           loadResult: LoadedGameResult,
                           startGameResult?: StartGameResult,
                           reinit?: boolean): Promise<ClientResponse> {
        try {
            if (flow.flowContext.logoutResult) {
                await this.login(flow, reinit);
            }
            this.setUpGame(flow.flowContext, flow.game);
            const result: BaseResult = await this.gameInit(flow.game, flow);
            this.addNoWinResultForLegacyGames(result as LegacyGameInitResult, loadResult);
            this.localizeGameName(result, startGameResult?.localizedGameName);

            await this.checkInitSettingsSaved(loadResult, result);

            const balance: BalanceResponse = await this.getBalance(flow);
            const extraData: ExtraData = flow.getExtraData();
            const tickers: TickersResponse = await this.gameJackpotTickers(flow);
            const jackpot: JackpotResults = await this.gameJackpotResult(flow);
            const clientResponse = {
                gameSession: flow.info().gameSessionId,
                balance,
                result,
                gameSettings: flow.gameData.gameSettings,
                brandSettings: flow.gameData.brandSettings,
                jrsdSettings: flow.gameData.jrsdSettings,
                jurisdictionCode: flow.gameData.jurisdictionCode,
                playedFromCountry: flow.gameData.playedFromCountry,
                roundEnded: flow.flowContext.roundEnded,
                renderType: flow.gameData.renderType,
                brandInfo: flow.gameData.brandInfo
            } as any;

            const clientSettings = this.getClientSettings(startGameResult);
            if (clientSettings) {
                clientResponse.clientSettings = clientSettings;
            }

            if (flow.gameData.currencyReplacement) {
                clientResponse.currencyReplacement = flow.gameData.currencyReplacement;
            }
            if (extraData) {
                clientResponse.extraData = extraData;
            }

            if (!flow.flowContext.roundEnded) {
                this.decorateResultWithRoundStatistics(clientResponse, result, flow.flowContext.round);
            }

            return this.responseWithJPInfo(clientResponse, tickers, jackpot);
        } finally {
            flow.close();
        }
    }

    private getClientSettings(startGameResult: StartGameResult): ClientSettings | undefined {
        if (startGameResult?.gameData?.player) {
            const playerInfo = startGameResult.gameData.player;
            return {
                ivp: playerInfo.isVip,
                nickname: playerInfo.nickname,
                hsWrn: playerInfo.hasWarn,
                sPblkCtBk: playerInfo.isPublicChatBlock,
                sPrlkCtBk: playerInfo.isPrivateChatBlock,
                niChAttLt: playerInfo.nicknameChangeAttemptsLeft,
            } as any;
        }
        return undefined;
    }

    private async login(flow: EngineGameFlow<GameInitRequest>, increaseLogoutId?: boolean) {
        if (flow.flowContext.logoutResult === MerchantLogoutResult.PENDING_LOGOUT) {
            await MerchantGameSessionService.logout(flow.flowContext);
        }
        return MerchantGameSessionService.login(flow.flowContext, increaseLogoutId);
    }

    protected async transferBalance(flow: EngineGameFlow<BalanceTransferRequest>,
                                    req: BalanceTransferRequest): Promise<ClientResponse> {
        try {
            const balance = await flow.transfer({
                operation: req.request,
                amount: req.amount
            });
            const ticker = await this.gameJackpotTickers(flow);
            return this.createResponse(req, flow, balance, { request: req.request }, ticker);
        } finally {
            flow.close();
        }
    }

    protected async getTicker(flow: EngineGameFlow<JackpotTickerRequest>,
                              req: JackpotTickerRequest): Promise<ClientResponse> {
        const ticker = await this.gameJackpotTickers(flow);
        const balance = req.getBalance === undefined || req.getBalance === true ?
            await this.getBalance(flow) :
            undefined;
        return this.createResponse(req, flow, balance, { request: req.request }, ticker);
    }

    protected async processPlay(flow: EngineGameFlow<GamePlayRequest>,
                                req: GamePlayRequest,
                                method: PlayMethod = this.gamePlay.bind(this)): Promise<ClientResponse> {
        try {
            ContextVariables.setUpRound(flow.flowContext.roundId);
            if (!flow.validateJackpotState()) {
                return Promise.reject(new JackpotStatusNotValid());
            }

            // reference to roundStatistics object. !NB, we can't use flow.flowContext.round later as it
            // gets erased after last spin in a round
            const roundStats = flow.flowContext.round;

            const result: unknown = await method(flow);
            let responseResult = result as GamePlayResponse;
            let balance;
            if (result && (result as LiveGamePlayResponse).response) {
                const liveResult = result as LiveGamePlayResponse;
                responseResult = liveResult.response;
                if (!("dontUseBalance" in liveResult) || !liveResult.dontUseBalance) {
                    balance = await this.getBalance(flow);
                }
            } else {
                balance = await this.getBalance(flow);
            }
            const tickers = await this.gameJackpotTickers(flow);
            const jackpot = await this.gameJackpotResult(flow);
            return this.createResponse(req, flow, balance, responseResult, tickers, jackpot, roundStats);
        } finally {
            flow.close();
        }
    }

    @measure({ name: "GameController.loadGame", isAsync: true, debugOnly: true })
    protected loadGame(module: string): Promise<LoadedGameResult> {
        return load(module);
    }

    @measure({ name: "Game.init", isAsync: true, debugOnly: true })
    private async gameInit(game: SomeGame, flow: EngineGameFlow<GameInitRequest>): Promise<BaseResult> {
        injectRandomGenerator(flow);
        return game.init(flow).catch(this.wrapGameError);
    }

    @measure({ name: "GameController.gamePlay", isAsync: true, debugOnly: true })
    protected gamePlay(flow: EngineGameFlow<GamePlayRequest>): Promise<GamePlayResponse | LiveGamePlayResponse> {
        injectRandomGenerator(flow);
        const game = flow.game;
        return game.play(flow, isAllowedSetPositionsByClient()).catch(this.wrapGameError);
    }

    @measure({ name: "GameController.internalEvent", isAsync: true, debugOnly: true })
    protected gameInternalEvent(
        flow: EngineGameFlow<GamePlayRequest>): Promise<GamePlayResponse | LiveGamePlayResponse> {
        injectRandomGenerator(flow);
        const game = flow.game;
        return game.internalEvent(flow).catch(this.wrapGameError);
    }

    protected async validateInitRequest(request: ExtendedGameInitRequest): Promise<StartGameTokenData> {
        const startGameTokenData = await extractStartGameTokenData(request);
        this.validateEnvId(startGameTokenData.envId);
        this.validateGameId(startGameTokenData, request.gameId);
        this.validateDeviceId(request);
        this.validateDeploymentRoute(startGameTokenData);
        return startGameTokenData;
    }

    @measure({ name: "GameController.gameJackpotTickers", isAsync: true, debugOnly: true })
    protected async gameJackpotTickers(flow: EngineGameFlow<GamePlayRequest>): Promise<TickersResponse> {
        const jackpotGame: JackpotGame<any, any> = flow.game as any;
        let tickers;
        if (jackpotGame && jackpotGame.getJackpotTickers) {
            tickers = await jackpotGame.getJackpotTickers(flow);
        } else {
            // default implementation
            tickers = await flow.jackpotTickers();
        }
        if (!tickers) {
            return;
        }
        const result: TickersResponse = [];
        for (const ticker of tickers) {
            const resultTicker = {
                jackpotId: ticker.jackpotId,
                jackpotType: ticker.jackpotType,
                jackpotBaseType: ticker.jackpotBaseType,
                pools: {}
            };
            for (const pool of Object.keys(ticker.pools)) {
                resultTicker.pools[pool] = {
                    amount: flow.toGameAmount(ticker.pools[pool].amount)
                };
                if (ticker.pools[pool].info) {
                    resultTicker.pools[pool].info = ticker.pools[pool].info;
                }
            }
            result.push(resultTicker);
        }
        return result;
    }

    @measure({ name: "GameController.gameJackpotResult", isAsync: true, debugOnly: true })
    protected async gameJackpotResult(flow: EngineGameFlow<GamePlayRequest>): Promise<JackpotResults> {
        const jackpotGame: JackpotGame<any, any> = flow.game as any;
        if (jackpotGame && jackpotGame.getJackpotResult && !JackpotUtil.isInstantJackpot(flow)) {
            return jackpotGame.getJackpotResult(flow);
        }

        // default implementation
        const jackpots: JackpotResults = await flow.jackpotResult();
        if (!jackpots) {
            return;
        }

        // for backward compatibility we return single object if there is single win and array for multiple win
        if (Array.isArray(jackpots)) {
            for (const jackpot of jackpots) {
                if (jackpot.event === "win") {
                    jackpot.amount = flow.toGameAmount(jackpot.amount);
                }
                jackpot.transactionId = undefined;
                jackpot.gameData = undefined;
            }
        } else {
            if (jackpots.event === "win") {
                jackpots.amount = flow.toGameAmount(jackpots.amount);
            }
            jackpots.transactionId = undefined;
            jackpots.gameData = undefined;
        }
        return jackpots;
    }

    // tslint:disable-next-line:no-empty
    protected setUpGame<T extends SomeGame>(flowContext: GameFlowContext, game: T) {
    }

    // tslint:disable-next-line:no-empty
    protected setUpContext(context: GameFlowContext, game: SomeGame) {
    }

    protected createResponse(request: GamePlayRequest,
                             flow: EngineGameFlow<BaseRequest>,
                             balance: BalanceResponse,
                             result: GamePlayResponse,
                             tickers?: TickersResponse,
                             jackpot?: JackpotResults,
                             roundStats?: RoundStatistics): ClientResponse {
        const extraData: ExtraData = flow.getExtraData();
        const response: ClientResponse = {
            gameSession: request.gameSession,
            balance,
            result,
            extraData,
            requestId: request.requestId,
            roundEnded: flow.flowContext.roundEnded
        };

        this.decorateResultWithRoundStatistics(response, result, roundStats, jackpot);

        return this.responseWithJPInfo(response,
            tickers,
            jackpot);
    }

    // adds roundTotalBet and roundTotalWin for 'play' event response from game client
    private decorateResultWithRoundStatistics(clientResponse: any,
                                              result: GamePlayResponse,
                                              roundStats?: RoundStatistics,
                                              jackpot?: JackpotResults): void {
        if (roundStats) {
            clientResponse.roundTotalBet = roundStats.totalBet;
            clientResponse.roundTotalWin = roundStats.totalWin;
            return;
        }

        // case when there is a single spin in a round
        if (result && result.totalBet !== undefined && result.totalWin !== undefined) {
            clientResponse.roundTotalBet = result.totalBet;
            clientResponse.roundTotalWin = result.totalWin;

            // and if in this single spin there was a jp win...
            if (jackpot) {
                if ((jackpot as JackpotResult).amount) {
                    clientResponse.roundTotalWin = result.totalWin + (jackpot as JackpotResult).amount;
                } else if (Array.isArray(jackpot)) {
                    const totalJpWinFromAllJPs = (jackpot as JackpotResult[])
                        .reduce((totalJPAmount, jp) => totalJPAmount + jp.amount, 0);
                    clientResponse.roundTotalWin = result.totalWin + totalJpWinFromAllJPs;
                }
            }
        }
    }

    protected responseWithJPInfo(response: ClientResponse,
                                 tickers?: TickersResponse,
                                 jackpot?: JackpotResults): ClientResponse {
        if (tickers && tickers.length) {
            response.ticker = tickers[0];
            response.tickers = tickers;
        }
        if (jackpot) {
            response.jackpot = jackpot;
        }
        return response;
    }

    protected validateGameId(startGameTokenData: StartGameTokenData, gameId: string): void {
        const isGameMatch = (startGameTokenData.providerGameCode && startGameTokenData.providerGameCode === gameId)
            || (!startGameTokenData.providerGameCode && PlayMode.isFunMode(startGameTokenData.playmode));
        if (!isGameMatch) {
            throw new GameNotMatch();
        }
    }

    protected async getBalance(flow: EngineGameFlow): Promise<BalanceResponse> {
        return flow.getBalance();
    }

    private async checkInitSettingsSaved(loadedGame: LoadedGameResult, result: BaseResult): Promise<void> {
        return GameInitSettings.checkIfSaved(loadedGame.moduleName, result);
    }

    @measure({ name: "GameController.chooseGame", isAsync: true })
    public async chooseGame(request: ChooseGameRequest & RequestContext): Promise<any> {
        if (request.gameSession) { // this field is required for GameFlowFactory.createForRequest()
            const flow: EngineGameFlow<GamePlayRequest> =
                await this.createFlowForRequest(request, false, false);
            flowDataLogger.info(flow.getLogData(), "ChooseGame");
            request.gameTokenData = flow.gameData.gameTokenData;
        }
        if ("isLive" in request && request.isLive) {
            return {
                result: {
                    request: request.request,
                    payload: await getPlayerGameInfoService().getPlayerGameURLInfo(request),
                }
            };
        }
        return getPlayerGameInfoService().getPlayerGameURLInfo(request);
    }

    @measure({ name: "GameController.getAvailableGames", isAsync: true })
    public async getAvailableGames(request: GamesListRequest): Promise<any> {
        return getPlayerGameInfoService().getAvailableGames(request);
    }

    @measure({ name: "GameController.redeemBNS", isAsync: true })
    public async redeemBNS(request: RedeemBNSRequest): Promise<ClientResponse> {
        const flow: EngineGameFlow<RedeemBNSRequest> = await this.createFlowForRequest(request, false, true);
        try {
            const balance = await flow.getBalance();
            if (balance.bonusCoins && balance.bonusCoins.redeemBalance
                && (!request.promoId || request.promoId === balance.bonusCoins.promoId)) {
                if (!flow.flowContext.roundEnded && request.manual) {
                    return Promise.reject(new ManualRedeemBNSError());
                } else {
                    return this.createResponse(request,
                        flow,
                        await flow.redeemBNS(balance.bonusCoins),
                        {
                            request: request.request,
                            redeemBalance: balance.bonusCoins.redeemBalance,
                            redeemCurrency: balance.bonusCoins.redeemCurrency,
                            promoId: balance.bonusCoins.promoId
                        } as RedeemBNSResponse);
                }
            } else {
                return Promise.reject(new RedeemBNSError());
            }
        } finally {
            flow.close();
        }
    }

    private validateEnvId(envId: string) {
        if (envId && config.environmentId && config.environmentId !== envId) {
            throw new Errors.EnvIdChangedError();
        }
    }

    private validateDeviceId(request: GameInitRequest) {
        if (!request.deviceId) {
            throw new Errors.ValidationError("DeviceId is missing");
        }
    }

    private validateDeploymentRoute(token: StartGameTokenData) {
        if (!config.routing.validateDeployment) {
            return;
        }

        const allowedDeployment = config.routing.allowedDeployment;

        const isRootRouting = allowedDeployment === "" || allowedDeployment === undefined;

        // We allow to play if the is no deployment field in the token and allowedDeployment is root ("") or absent
        if (!token.deployment && isRootRouting) {
            return;
        }

        if (config.routing.allowedDeployment !== token.deployment) {
            throw new Errors.ValidationError("Invalid route");
        }
    }

    private wrapGameError(err) {
        if (err instanceof SWError) {
            return Promise.reject(err.setSpecialFlag(ErrorSpecialFlag.GAME_MODULE_ERROR));
        } else if (isSWError(err)) { // we can have a SWError that does not have the setSpecialFlag method
            const convertedError = new SWError(err.responseStatus, err.code, err.message, ERROR_LEVEL.ERROR);
            return Promise.reject(convertedError.setSpecialFlag(ErrorSpecialFlag.GAME_MODULE_ERROR));
        }
        return Promise.reject(new GameError(err));
    }

    private addNoWinResultForLegacyGames(gameInitResult: LegacyGameInitResult, loadResult: LoadedGameResult) {
        if (!gameInitResult?.defaultResult) {
            const { noWinResult } = loadResult;
            if (noWinResult) {
                gameInitResult.defaultResult = noWinResult;
            }
        }
    }

    private localizeGameName(result, localizedGameName?: string) {
        if (localizedGameName) {
            result.name = localizedGameName;
        }
    }

    protected checkIfMerchantSessionStateful(ctx: GameFlowContext, startGameTokenData: StartGameTokenData) {
        if (ctx?.broken && startGameTokenData.merchantSessionStateful &&
            startGameTokenData.merchantSessionId !== ctx?.gameData?.gameTokenData?.merchantSessionId) {
            throw new Errors.CannotStartNewMerchantPlayerSessionError();
        }
    }

    @measure({ name: "GameController.changeNickname", isAsync: true })
    public async changeNickname(request: ChangeNicknameActionRequest): Promise<ClientResponse> {
        flowDataLogger.info({ request }, "Change-nickname");
        const service = getChangeNicknameActionService();
        const flow: EngineGameFlow<GamePlayRequest> = await this.createFlowForRequest(request, true, false);
        const responseAction = await service.performChangeNicknameAction(request, flow.flowContext);
        const response: ClientResponse = {
            gameSession: flow.info().gameSessionId,
            balance: undefined,
            result: { request: request.request }
        };

        if (responseAction.code) {
            response.result["payload"] = { code: responseAction.code, message: responseAction.message };
            return response;
        }
        this.updateContextWithData(responseAction, flow.flowContext.gameData);
        response.result["payload"] = {
            nickname: responseAction.nickname,
            niChAttLt: responseAction.nicknameChangeAttemptsLeft
        };

        await flow.flowContext.updateGameData();

        return response;
    }

    private updateContextWithData(responseAction: any, gameData: GameData): void {
        if (gameData?.player) {
            gameData.player.nickname = responseAction.nickname;
            gameData.player.nicknameChangeAttemptsLeft = responseAction.nicknameChangeAttemptsLeft;
        } else {
            gameData.player = {
                ...gameData.player,
                nickname: responseAction.nickname,
                nicknameChangeAttemptsLeft: responseAction.nicknameChangeAttemptsLeft
            };
        }
    }
}

interface LegacyGameInitResult extends BaseResult {
    defaultResult: BaseResult;
}
